<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>3D Korku Kaçış Oyunu - Gözcüler</title>
    <style>
      body {
        margin: 0;
        overflow: hidden;
        background-color: #000;
        color: #fff;
        font-family: "Courier New", Courier, monospace;
      }
      canvas {
        display: block;
      }
      #main-canvas {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
      }
      #ui-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        z-index: 3;
      }
      #crosshair {
        width: 10px;
        height: 10px;
        border: 2px solid white;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: none;
        mix-blend-mode: difference;
      }
      .message {
        font-size: 2em;
        padding: 20px;
        background-color: rgba(0, 0, 0, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        display: none;
        text-shadow: 2px 2px 5px #000;
      }
      #start-screen,
      #game-over-screen {
        pointer-events: auto;
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.85);
        z-index: 10;
        width: 100%;
        height: 100%;
      }
      #start-screen h1,
      #game-over-screen h1 {
        font-size: 3em;
        margin-bottom: 20px;
        letter-spacing: 3px;
      }
      #start-screen p,
      #game-over-screen p {
        font-size: 1.2em;
      }
      .ui-bar-container {
        position: absolute;
        bottom: 20px;
        width: 200px;
        height: 15px;
        border: 2px solid rgba(255, 255, 255, 0.5);
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        display: none;
        padding: 2px;
      }
      #stamina-container {
        left: 20px;
      }
      #battery-container {
        right: 20px;
      }
      .ui-bar {
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        width: 100%;
        transition: width 0.2s linear;
        border-radius: 3px;
      }
      #stamina-bar {
        background-color: #4caf50;
      }
      #battery-bar {
        background-color: #ffc107;
      }
      #spare-batteries-ui {
        position: absolute;
        bottom: 45px;
        right: 20px;
        font-size: 1.5em;
        text-shadow: 2px 2px 4px #000;
        display: none;
        color: white;
        font-family: "Courier New", monospace;
        font-weight: bold;
      }
      #level-display {
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 2em;
        text-shadow: 2px 2px 4px #000;
        color: white;
        font-family: "Courier New", monospace;
        font-weight: bold;
        z-index: 10;
        display: none;
      }
      #money-display {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 1.8em;
        text-shadow: 2px 2px 4px #000;
        color: #ffd700;
        font-family: "Courier New", monospace;
        font-weight: bold;
        z-index: 10;
        display: none;
      }
      #shop-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: none;
        z-index: 100;
        justify-content: center;
        align-items: center;
      }
      #shop-container {
        background-color: rgba(20, 20, 20, 0.95);
        border: 3px solid #ffd700;
        border-radius: 10px;
        padding: 30px;
        max-width: 600px;
        width: 90%;
        max-height: 80%;
        overflow-y: auto;
      }
      #shop-title {
        text-align: center;
        font-size: 2.5em;
        color: #ffd700;
        margin-bottom: 20px;
        font-family: "Courier New", monospace;
        text-shadow: 2px 2px 4px #000;
      }
      #shop-money {
        text-align: center;
        font-size: 1.5em;
        color: #ffd700;
        margin-bottom: 30px;
        font-family: "Courier New", monospace;
      }
      .shop-item {
        background-color: rgba(40, 40, 40, 0.8);
        border: 2px solid #666;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .shop-item:hover {
        border-color: #ffd700;
        background-color: rgba(60, 60, 40, 0.8);
      }
      .shop-item-info {
        flex: 1;
      }
      .shop-item-name {
        font-size: 1.3em;
        color: white;
        font-weight: bold;
        margin-bottom: 5px;
      }
      .shop-item-description {
        font-size: 1em;
        color: #ccc;
        margin-bottom: 5px;
      }
      .shop-item-cost {
        font-size: 1.1em;
        color: #ffd700;
        font-weight: bold;
      }
      .shop-buy-button {
        background-color: #4caf50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1.1em;
        font-weight: bold;
      }
      .shop-buy-button:hover {
        background-color: #45a049;
      }
      .shop-buy-button:disabled {
        background-color: #666;
        cursor: not-allowed;
      }
      .shop-instructions {
        text-align: center;
        color: #ccc;
        margin-top: 20px;
        font-style: italic;
      }

      .shop-reset-section {
        margin-top: 30px;
        text-align: center;
        border-top: 1px solid #444;
        padding-top: 20px;
      }

      .shop-reset-button {
        background: linear-gradient(45deg, #ff4444, #cc0000);
        color: white;
        border: none;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: bold;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }

      .shop-reset-button:hover {
        background: linear-gradient(45deg, #ff6666, #ff0000);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 68, 68, 0.3);
      }

      .reset-warning {
        color: #ff6666;
        font-size: 12px;
        margin-top: 8px;
        font-style: italic;
      }
      #weapon-display {
        position: absolute;
        bottom: 20px;
        right: 20px;
        font-size: 1.5em;
        text-shadow: 2px 2px 4px #000;
        color: #ff6666;
        font-family: "Courier New", monospace;
        font-weight: bold;
        z-index: 10;
        display: none;
      }
      #whisper-message {
        position: absolute;
        bottom: 80px;
        font-size: 1.5em;
        color: #ff4444;
        text-shadow: 1px 1px 5px #000;
        opacity: 0;
        transition: opacity 0.5s;
        max-width: 80%;
      }
      #minimap-container {
        position: absolute;
        top: 20px;
        right: 20px;
        border: 2px solid rgba(255, 255, 255, 0.5);
        background-color: rgba(0, 0, 0, 0.7);
        display: none;
        z-index: 2;
      }
      #minimap-canvas {
        display: block;
      }
      #hiding-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
          ellipse at center,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0.8) 30%,
          rgba(0, 0, 0, 0.98) 60%,
          #000000 100%
        );
        z-index: 5;
        display: none;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 1.5em;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <div id="ui-container">
      <div
        id="start-screen"
        class="message"
        style="display: flex; flex-direction: column"
      >
        <h1>GÖZCÜLER</h1>
        <p>Bir sonraki seviyeye geçmek için anahtarı bul.</p>
        <p>Sadece ışıktan korkarlar.</p>
        <p style="font-size: 0.8em; color: #aaa">
          ✨ Geliştirilmiş Düşman Mantığı
        </p>
        <br />
        <p><strong>Başlamak için Tıkla</strong></p>
        <p>(F: Fener | R: Batarya | E: Etkileşim/Saklan)</p>
      </div>
      <div id="game-over-screen" class="message">
        <h1>Oyun Bitti</h1>
        <p id="game-over-taunt">Kaçamadın.</p>
        <p style="font-size: 0.8em">Yeniden Başlamak için Tıkla</p>
      </div>
      <div
        id="key-message"
        class="message"
        style="position: absolute; top: 10%; display: none"
      >
        Anahtar Bulundu! Çıkışı ara.
      </div>
      <div
        id="door-locked-message"
        class="message"
        style="position: absolute; top: 10%; display: none"
      >
        Kapı Kilitli. Anahtarı bulmalısın.
      </div>
      <div id="crosshair"></div>
      <div id="level-display">Level: 1</div>
      <div id="money-display">Coins: 0</div>
      <div id="stamina-container" class="ui-bar-container">
        <div id="stamina-bar" class="ui-bar"></div>
      </div>
      <div id="battery-container" class="ui-bar-container">
        <div id="battery-bar" class="ui-bar"></div>
      </div>
      <div id="spare-batteries-ui">🔋 0</div>
      <div id="weapon-display">⚡ 0/5</div>
      <div id="whisper-message"></div>
    </div>
    <div id="minimap-container">
      <canvas id="minimap-canvas"></canvas>
    </div>
    <div id="hiding-overlay">Nefesini tut... (Çıkmak için E'ye bas)</div>

    <!-- Shop Overlay -->
    <div id="shop-overlay">
      <div id="shop-container">
        <div id="shop-title">UPGRADE SHOP</div>
        <div id="shop-money">Coins: 0</div>
        <div id="shop-items">
          <div class="shop-item" data-upgrade="speed">
            <div class="shop-item-info">
              <div class="shop-item-name">Speed Boost</div>
              <div class="shop-item-description">
                Increase running speed permanently
              </div>
              <div class="shop-item-cost">Cost: 100 coins</div>
            </div>
            <button class="shop-buy-button" onclick="buyUpgrade('speed')">
              BUY
            </button>
          </div>
          <div class="shop-item" data-upgrade="battery">
            <div class="shop-item-info">
              <div class="shop-item-name">Battery Extension</div>
              <div class="shop-item-description">
                Increase maximum flashlight battery capacity
              </div>
              <div class="shop-item-cost">Cost: 150 coins</div>
            </div>
            <button class="shop-buy-button" onclick="buyUpgrade('battery')">
              BUY
            </button>
          </div>
          <div class="shop-item" data-upgrade="flashlight-red">
            <div class="shop-item-info">
              <div class="shop-item-name">Red Flashlight</div>
              <div class="shop-item-description">
                Change flashlight color to red
              </div>
              <div class="shop-item-cost">Cost: 75 coins</div>
            </div>
            <button
              class="shop-buy-button"
              onclick="buyUpgrade('flashlight-red')"
            >
              BUY
            </button>
          </div>
          <div class="shop-item" data-upgrade="flashlight-blue">
            <div class="shop-item-info">
              <div class="shop-item-name">Blue Flashlight</div>
              <div class="shop-item-description">
                Change flashlight color to blue
              </div>
              <div class="shop-item-cost">Cost: 75 coins</div>
            </div>
            <button
              class="shop-buy-button"
              onclick="buyUpgrade('flashlight-blue')"
            >
              BUY
            </button>
          </div>
          <div class="shop-item" data-upgrade="flashlight-green">
            <div class="shop-item-info">
              <div class="shop-item-name">Green Flashlight</div>
              <div class="shop-item-description">
                Change flashlight color to green
              </div>
              <div class="shop-item-cost">Cost: 75 coins</div>
            </div>
            <button
              class="shop-buy-button"
              onclick="buyUpgrade('flashlight-green')"
            >
              BUY
            </button>
          </div>
          <div class="shop-item" data-upgrade="weapon">
            <div class="shop-item-info">
              <div class="shop-item-name">Stun Gun</div>
              <div class="shop-item-description">
                Purchase a stun gun with 5 shots
              </div>
              <div class="shop-item-cost">Cost: 200 coins</div>
            </div>
            <button class="shop-buy-button" onclick="buyUpgrade('weapon')">
              BUY
            </button>
          </div>
        </div>
        <div class="shop-instructions">Press TAB or ESC to close shop</div>
        <div class="shop-reset-section">
          <button class="shop-reset-button" onclick="resetGame()">
            🔄 RESET GAME
          </button>
          <div class="reset-warning">This will delete all progress!</div>
        </div>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.7.77/Tone.js"></script>

    <script>
      "use strict";
      // --- TEMEL DEĞİŞKENLER ---
      let scene, camera, renderer, ambientLight;
      let player, playerColliderMesh, flashlight;
      const clock = new THREE.Clock();
      const raycaster = new THREE.Raycaster();
      const textureLoader = new THREE.TextureLoader();

      // --- MİNİ HARİTA ---
      let minimapCanvas, minimapCtx, visitedMap;
      const MINIMAP_CELL_SIZE = 8;

      // --- SES ---
      const sounds = {};
      let audioInitialized = false;

      // --- OYUN DURUMU ---
      let level = 1,
        hasKey = false,
        gameActive = false,
        isGameOver = false;
      const collidableObjects = [];
      let collectibles = [];
      let interactables = [];
      let key, door;
      let enemies = [];

      // --- CURRENCY SYSTEM ---
      let playerMoney = 0;
      let coins = [];
      const COINS_PER_LEVEL = 4; // 3-5 coins per level (will randomize)
      const LEVEL_COMPLETION_BONUS = 50;

      // --- UPGRADE SYSTEM ---
      let isShopOpen = false;
      let upgrades = {
        speed: false,
        battery: false,
        flashlightColor: "white", // "white", "red", "blue", "green"
        weapon: false,
      };
      const UPGRADE_COSTS = {
        speed: 100,
        battery: 150,
        "flashlight-red": 75,
        "flashlight-blue": 75,
        "flashlight-green": 75,
        weapon: 200,
      };

      // --- WEAPON SYSTEM ---
      let weaponAmmo = 0;
      const MAX_WEAPON_AMMO = 5;
      let projectiles = [];
      const PROJECTILE_SPEED = 15;
      const STUN_DURATION = 10; // seconds
      let mazeMap;
      const WALL_SIZE = 2.5;
      const WALL_HEIGHT = 4;

      // --- OYUNCU AYARLARI ---
      const moveState = {
        forward: false,
        backward: false,
        left: false,
        right: false,
        sprint: false,
      };
      const playerVelocity = new THREE.Vector3();
      const PLAYER_BASE_SPEED = 3.5;
      const SPRINT_MULTIPLIER = 1.8;
      const PLAYER_HEIGHT = 1.8;
      const playerCollider = new THREE.Box3();
      const PLAYER_BODY_SIZE = new THREE.Vector3(0.7, PLAYER_HEIGHT, 0.7);
      let bobTime = 0,
        footstepCooldown = 0,
        isHiding = false;

      // --- FENER & DAYANIKLILIK ---
      let isFlashlightOn = true;
      let stamina = 100;
      const MAX_STAMINA = 100;
      const STAMINA_DRAIN_RATE = 35;
      const STAMINA_REGEN_RATE = 15;
      const STAMINA_REGEN_DELAY = 2;
      let staminaRegenCooldown = 0;
      let flashlightBattery = 100;
      const MAX_FLASHLIGHT_BATTERY = 100;
      const FLASHLIGHT_DRAIN_RATE = 3.0; // Pili biraz daha yavaş bitiyor
      let spareBatteries = 0;
      const BASE_FLASHLIGHT_INTENSITY = 2;

      // --- DÜŞMAN AYARLARI ---
      const ENEMY_BASE_SPEED = 3.5;
      const ENEMY_AI_STATE = {
        PATROL: "PATROL",
        CHASE: "CHASE",
        SEARCH: "SEARCH",
      };

      // --- GEMINI ---
      let whisperCooldown = 15.0;

      // LOCAL STORAGE SYSTEM
      function saveGame() {
        const gameData = {
          level: level,
          playerMoney: playerMoney,
          upgrades: upgrades,
          spareBatteries: spareBatteries,
          weaponAmmo: weaponAmmo,
          maxFlashlightBattery: maxFlashlightBattery,
        };
        localStorage.setItem("escapeFromMazeGame", JSON.stringify(gameData));
      }

      function loadGame() {
        try {
          const savedData = localStorage.getItem("escapeFromMazeGame");
          if (savedData) {
            const gameData = JSON.parse(savedData);
            level = gameData.level || 1;
            playerMoney = gameData.playerMoney || 0;
            upgrades = gameData.upgrades || {
              speed: false,
              stamina: false,
              battery: false,
              weapon: false,
            };
            spareBatteries = gameData.spareBatteries || 3;
            weaponAmmo = gameData.weaponAmmo || MAX_WEAPON_AMMO;
            maxFlashlightBattery = gameData.maxFlashlightBattery || 100;
            console.log("Game loaded from save");
          }
        } catch (error) {
          console.warn("Failed to load game:", error);
        }
      }

      function resetGame() {
        if (
          confirm(
            "Are you sure you want to reset all progress? This cannot be undone!"
          )
        ) {
          // Clear local storage
          localStorage.removeItem("escapeFromMazeGame");

          // Reset all game variables to default
          level = 1;
          playerMoney = 0;
          upgrades = {
            speed: false,
            stamina: false,
            battery: false,
            weapon: false,
          };
          spareBatteries = 3;
          weaponAmmo = MAX_WEAPON_AMMO;
          maxFlashlightBattery = 100;
          flashlightBattery = maxFlashlightBattery;
          stamina = MAX_STAMINA;

          // Close shop and restart game
          closeShop();

          // Clear current level and restart
          clearLevel();
          loadLevel(1);

          console.log("Game reset to defaults");
        }
      }

      // OYUNU BAŞLATMA - DOM yüklendikten sonra
      document.addEventListener("DOMContentLoaded", function () {
        init();
      });

      /**
       * Ana sahne, kamera, renderer ve temel oyun nesnelerini oluşturur.
       */
      function init() {
        // Load saved game data first
        loadGame();

        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x000000);
        scene.fog = new THREE.Fog(0x000000, 2, 15);

        ambientLight = new THREE.AmbientLight(0x101015);
        scene.add(ambientLight);

        camera = new THREE.PerspectiveCamera(
          75,
          window.innerWidth / window.innerHeight,
          0.1,
          1000
        );
        camera.position.y = PLAYER_HEIGHT * 0.4;

        player = new THREE.Object3D();
        player.add(camera);
        scene.add(player);

        // GÖRÜNMEZ OYUNCU ÇARPIŞMA KUTUSU (Düşman Raycast'i için)
        const playerBoxGeo = new THREE.BoxGeometry(
          PLAYER_BODY_SIZE.x,
          PLAYER_BODY_SIZE.y,
          PLAYER_BODY_SIZE.z
        );
        const playerBoxMat = new THREE.MeshBasicMaterial({ visible: false });
        playerColliderMesh = new THREE.Mesh(playerBoxGeo, playerBoxMat);
        playerColliderMesh.position.y = PLAYER_BODY_SIZE.y / 2;
        player.add(playerColliderMesh);

        flashlight = new THREE.SpotLight(
          0xffffff,
          BASE_FLASHLIGHT_INTENSITY,
          18,
          Math.PI / 5,
          0.5,
          1.5
        );
        flashlight.castShadow = true;
        camera.add(flashlight);
        flashlight.position.set(0, 0, 0.1);
        flashlight.target = camera;
        updateFlashlightColor(); // Apply any purchased color upgrades

        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.domElement.id = "main-canvas";
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(renderer.domElement);

        minimapCanvas = document.getElementById("minimap-canvas");
        minimapCtx = minimapCanvas.getContext("2d");

        setupEventListeners();
        animate();
      }

      /**
       * Creates a coin model with golden appearance and rotation animation
       */
      function createCoinModel() {
        const coinGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.1, 8);
        const coinMaterial = new THREE.MeshLambertMaterial({
          color: 0xffd700,
          emissive: 0x332200,
        });
        const coin = new THREE.Mesh(coinGeometry, coinMaterial);
        coin.name = "coin";
        coin.userData = { rotationSpeed: Math.random() * 2 + 1 }; // Random rotation speed
        return coin;
      }

      /**
       * Opens the upgrade shop
       */
      function openShop() {
        console.log("openShop called, current isShopOpen:", isShopOpen);
        if (isShopOpen) return;
        const shopOverlay = document.getElementById("shop-overlay");
        const shopMoney = document.getElementById("shop-money");
        if (!shopOverlay || !shopMoney) {
          console.log("Shop elements not found!");
          return;
        }

        isShopOpen = true;
        shopOverlay.style.display = "flex";
        shopMoney.innerText = `Coins: ${playerMoney}`;
        updateShopButtons();
        document.exitPointerLock();
        console.log("Shop opened, isShopOpen now:", isShopOpen);
      }

      /**
       * Closes the upgrade shop
       */
      function closeShop() {
        console.log("closeShop called, current isShopOpen:", isShopOpen);
        if (!isShopOpen) return;
        const shopOverlay = document.getElementById("shop-overlay");
        if (!shopOverlay) {
          console.log("Shop overlay not found!");
          return;
        }

        isShopOpen = false;
        shopOverlay.style.display = "none";
        if (gameActive) {
          document.body.requestPointerLock();
        }
        console.log("Shop closed, isShopOpen now:", isShopOpen);
      }

      /**
       * Updates shop button states based on player money and purchased upgrades
       */
      function updateShopButtons() {
        const buttons = document.querySelectorAll(".shop-buy-button");
        buttons.forEach((button) => {
          const upgradeType = button.parentElement.dataset.upgrade;
          const cost = UPGRADE_COSTS[upgradeType];
          const canAfford = playerMoney >= cost;

          // Check if already purchased
          let alreadyPurchased = false;
          if (upgradeType === "speed" && upgrades.speed)
            alreadyPurchased = true;
          if (upgradeType === "battery" && upgrades.battery)
            alreadyPurchased = true;
          if (upgradeType === "weapon" && upgrades.weapon)
            alreadyPurchased = true;
          if (
            upgradeType.startsWith("flashlight-") &&
            upgrades.flashlightColor === upgradeType.split("-")[1]
          )
            alreadyPurchased = true;

          button.disabled = !canAfford || alreadyPurchased;
          button.innerText = alreadyPurchased ? "OWNED" : "BUY";
        });
      }

      /**
       * Purchases an upgrade
       */
      function buyUpgrade(upgradeType) {
        console.log("buyUpgrade called for:", upgradeType);
        const cost = UPGRADE_COSTS[upgradeType];
        console.log("Cost:", cost, "Player money:", playerMoney);
        if (playerMoney < cost) {
          console.log("Not enough money!");
          return;
        }

        playerMoney -= cost;
        console.log("Money deducted, new amount:", playerMoney);

        // Apply upgrade
        console.log("Applying upgrade:", upgradeType);
        switch (upgradeType) {
          case "speed":
            upgrades.speed = true;
            console.log("Speed upgrade applied");
            break;
          case "battery":
            upgrades.battery = true;
            break;
          case "flashlight-red":
            upgrades.flashlightColor = "red";
            updateFlashlightColor();
            break;
          case "flashlight-blue":
            upgrades.flashlightColor = "blue";
            updateFlashlightColor();
            break;
          case "flashlight-green":
            upgrades.flashlightColor = "green";
            updateFlashlightColor();
            break;
          case "weapon":
            upgrades.weapon = true;
            weaponAmmo = MAX_WEAPON_AMMO; // Give full ammo when purchased
            break;
        }

        // Save progress and update shop display
        saveGame();
        console.log("Updating shop display with money:", playerMoney);
        document.getElementById(
          "shop-money"
        ).innerText = `Coins: ${playerMoney}`;
        updateShopButtons();
        console.log("buyUpgrade completed for:", upgradeType);
      }

      /**
       * Updates flashlight color based on upgrade
       */
      function updateFlashlightColor() {
        if (!flashlight) return;

        switch (upgrades.flashlightColor) {
          case "red":
            flashlight.color.setHex(0xff4444);
            break;
          case "blue":
            flashlight.color.setHex(0x4444ff);
            break;
          case "green":
            flashlight.color.setHex(0x44ff44);
            break;
          default:
            flashlight.color.setHex(0xffffff);
            break;
        }
      }

      /**
       * Gets current maximum flashlight battery capacity
       */
      function getMaxFlashlightBattery() {
        return upgrades.battery
          ? MAX_FLASHLIGHT_BATTERY * 1.5
          : MAX_FLASHLIGHT_BATTERY;
      }

      /**
       * Creates a projectile for the stun gun
       */
      function createProjectile() {
        const projectileGeometry = new THREE.SphereGeometry(0.05, 8, 8);
        const projectileMaterial = new THREE.MeshBasicMaterial({
          color: 0x00ffff,
          emissive: 0x004444,
        });
        const projectile = new THREE.Mesh(
          projectileGeometry,
          projectileMaterial
        );

        // Set initial position and direction
        projectile.position.copy(camera.position);
        projectile.position.add(
          camera.getWorldDirection(new THREE.Vector3()).multiplyScalar(0.5)
        );

        // Store direction for movement
        projectile.userData = {
          direction: camera.getWorldDirection(new THREE.Vector3()),
          speed: PROJECTILE_SPEED,
          lifetime: 3.0, // 3 seconds max lifetime
        };

        return projectile;
      }

      /**
       * Fires the stun gun weapon
       */
      function fireWeapon() {
        if (!upgrades.weapon || weaponAmmo <= 0) return;

        weaponAmmo--;

        // Create and add projectile
        const projectile = createProjectile();
        scene.add(projectile);
        projectiles.push(projectile);

        // Play weapon sound (reuse existing sound)
        if (audioInitialized) {
          sounds.keyPickup.triggerAttackRelease("A4", "16n");
        }
      }

      /**
       * Updates all projectiles
       */
      function updateProjectiles(deltaTime) {
        for (let i = projectiles.length - 1; i >= 0; i--) {
          const projectile = projectiles[i];

          // Move projectile
          const movement = projectile.userData.direction
            .clone()
            .multiplyScalar(projectile.userData.speed * deltaTime);
          projectile.position.add(movement);

          // Decrease lifetime
          projectile.userData.lifetime -= deltaTime;

          // Check for collision with enemies
          let hitEnemy = false;
          for (const enemy of enemies) {
            if (projectile.position.distanceTo(enemy.model.position) < 1.0) {
              // Stun the enemy
              enemy.stunned = true;
              enemy.stunTimer = STUN_DURATION;
              hitEnemy = true;
              break;
            }
          }

          // Check for collision with walls
          const projectileBox = new THREE.Box3().setFromObject(projectile);
          let hitWall = false;
          for (const wall of collidableObjects) {
            const wallBox = new THREE.Box3().setFromObject(wall);
            if (projectileBox.intersectsBox(wallBox)) {
              hitWall = true;
              break;
            }
          }

          // Remove projectile if it hit something or expired
          if (hitEnemy || hitWall || projectile.userData.lifetime <= 0) {
            scene.remove(projectile);
            projectiles.splice(i, 1);

            // Dispose geometry and material
            projectile.geometry.dispose();
            projectile.material.dispose();
          }
        }
      }

      /**
       * Oyun seslerini Tone.js kullanarak başlatır.
       */
      function initAudio() {
        if (audioInitialized) return;
        try {
          Tone.start();
          Tone.Destination.volume.value = 5;

          sounds.footstep = new Tone.MembraneSynth({
            pitchDecay: 0.01,
            octaves: 8,
            envelope: { attack: 0.001, decay: 0.2, sustain: 0 },
            volume: -12,
          }).toDestination();
          sounds.enemyFootstep = new Tone.MembraneSynth({
            pitchDecay: 0.04,
            octaves: 3,
            envelope: { attack: 0.01, decay: 0.4, sustain: 0 },
            volume: 0,
          }).toDestination();
          sounds.keyPickup = new Tone.Synth({
            envelope: { attack: 0.01, decay: 0.2, release: 0.2 },
            volume: -5,
          }).toDestination();
          sounds.batteryPickup = new Tone.Synth({
            oscillator: { type: "square" },
            envelope: { attack: 0.01, decay: 0.1, release: 0.1 },
            volume: -10,
          }).toDestination();
          sounds.coinPickup = new Tone.Synth({
            oscillator: { type: "triangle" },
            envelope: { attack: 0.01, decay: 0.3, release: 0.2 },
            volume: -8,
          }).toDestination();
          sounds.reload = new Tone.NoiseSynth({
            noise: { type: "white" },
            envelope: { attack: 0.01, decay: 0.15, sustain: 0 },
            volume: -15,
          }).toDestination();
          sounds.reloadFail = new Tone.MembraneSynth({
            pitchDecay: 0.05,
            octaves: 1,
            envelope: { attack: 0.001, decay: 0.15, sustain: 0 },
            volume: -15,
          }).toDestination();
          sounds.doorLocked = new Tone.MembraneSynth({
            pitchDecay: 0.1,
            octaves: 2,
            envelope: { attack: 0.01, decay: 0.5, sustain: 0 },
            volume: -10,
          }).toDestination();
          sounds.gameOver = new Tone.NoiseSynth({
            noise: { type: "pink" },
            envelope: { attack: 0.01, decay: 1.5, sustain: 0.1, release: 1 },
            volume: -5,
          }).toDestination();
          sounds.ambience = new Tone.NoiseSynth({
            noise: { type: "brown" },
            envelope: { attack: 5, decay: 2, sustain: 1 },
            volume: -30,
          }).toDestination();
          sounds.ambience.triggerAttack();
          sounds.hide = new Tone.MembraneSynth({
            pitchDecay: 0.2,
            octaves: 2,
            envelope: { attack: 0.01, decay: 0.3, sustain: 0 },
            volume: -12,
          }).toDestination();
          sounds.unhide = new Tone.MembraneSynth({
            pitchDecay: 0.2,
            octaves: 3,
            envelope: { attack: 0.01, decay: 0.2, sustain: 0 },
            volume: -12,
          }).toDestination();

          const musicSynth = new Tone.FMSynth({
            harmonicity: 3.01,
            modulationIndex: 14,
            envelope: { attack: 0.2, decay: 1 },
            modulation: { type: "sine" },
            modulationEnvelope: { attack: 0.2, decay: 1 },
            volume: -30,
          }).toDestination();
          const musicPattern = new Tone.Sequence(
            (time, note) => {
              musicSynth.triggerAttackRelease(note, "1n", time);
            },
            ["C2", ["Eb2", "D2"], "G1", null],
            "1n"
          );
          musicPattern.start(0);
          musicPattern.loop = true;
          Tone.Transport.start();
          audioInitialized = true;
        } catch (error) {
          console.warn("Audio setup failed:", error);
        }
      }

      /**
       * Recursive backtracker algoritması kullanarak bir labirent haritası oluşturur.
       */
      function generateMaze(width, height) {
        const map = Array(height)
          .fill(null)
          .map(() => Array(width).fill(1));
        const stack = [];
        const startX = 1,
          startY = 1;
        map[startY][startX] = 0;
        stack.push([startX, startY]);

        while (stack.length > 0) {
          const [cx, cy] = stack[stack.length - 1];
          const neighbors = [];
          if (cx > 1 && map[cy][cx - 2] === 1)
            neighbors.push([cx - 2, cy, cx - 1, cy]);
          if (cx < width - 2 && map[cy][cx + 2] === 1)
            neighbors.push([cx + 2, cy, cx + 1, cy]);
          if (cy > 1 && map[cy - 2][cx] === 1)
            neighbors.push([cx, cy - 2, cx, cy - 1]);
          if (cy < height - 2 && map[cy + 2][cx] === 1)
            neighbors.push([cx, cy + 2, cx, cy + 1]);

          if (neighbors.length > 0) {
            const [nx, ny, px, py] =
              neighbors[Math.floor(Math.random() * neighbors.length)];
            map[ny][nx] = 0;
            map[py][px] = 0;
            stack.push([nx, ny]);
          } else {
            stack.pop();
          }
        }
        return map;
      }

      /**
       * Gözcü düşmanın 3D modelini oluşturur.
       */
      function createEnemyModel() {
        const group = new THREE.Group();
        const material = new THREE.MeshStandardMaterial({
          color: 0xeeeeee,
          roughness: 0.8,
          metalness: 0.1,
          emissive: 0xeeeeee,
          emissiveIntensity: 0.2,
        });
        const head = new THREE.Mesh(
          new THREE.SphereGeometry(0.3, 16, 12),
          material
        );
        head.position.y = 0.6;
        const torso = new THREE.Mesh(
          new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8),
          material
        );
        torso.position.y = -0.2;
        const base = new THREE.Mesh(
          new THREE.CylinderGeometry(0.5, 0.5, 0.2, 12),
          material
        );
        base.position.y = -0.9;
        group.add(head, torso, base);
        group.children.forEach((c) => {
          c.castShadow = true;
          c.receiveShadow = true;
        });
        return group;
      }

      /**
       * Mevcut seviyeyi temizler ve yeni bir seviye oluşturur.
       */
      function loadLevel(levelNum) {
        // Comprehensive scene cleanup with proper geometry and material disposal

        // Remove and dispose collidable objects (walls, doors)
        collidableObjects.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });
        collidableObjects.length = 0;

        // Remove and dispose collectibles (keys, batteries)
        collectibles.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });
        collectibles.length = 0;

        // Remove and dispose coins
        coins.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });
        coins.length = 0;

        // Remove and dispose projectiles
        projectiles.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });
        projectiles.length = 0;

        // Remove and dispose interactables (closets)
        interactables.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });
        interactables.length = 0;

        // Remove and dispose enemies
        enemies.forEach((e) => {
          scene.remove(e.model);
          // Dispose enemy model geometry and materials
          e.model.traverse((child) => {
            if (child.geometry) child.geometry.dispose();
            if (child.material) {
              if (child.material.map) child.material.map.dispose();
              child.material.dispose();
            }
          });
        });
        enemies.length = 0;

        // Remove and dispose key
        if (key) {
          scene.remove(key);
          if (key.geometry) key.geometry.dispose();
          if (key.material) {
            if (key.material.map) key.material.map.dispose();
            key.material.dispose();
          }
          key = null;
        }

        // Remove and dispose door
        if (door) {
          scene.remove(door);
          if (door.geometry) door.geometry.dispose();
          if (door.material) {
            if (door.material.map) door.material.map.dispose();
            door.material.dispose();
          }
          door = null;
        }

        // Find and remove any existing floor objects from previous levels
        const objectsToRemove = [];
        scene.traverse((child) => {
          if (child.geometry && child.geometry.type === "PlaneGeometry") {
            objectsToRemove.push(child);
          }
        });

        objectsToRemove.forEach((obj) => {
          scene.remove(obj);
          if (obj.geometry) obj.geometry.dispose();
          if (obj.material) {
            if (obj.material.map) obj.material.map.dispose();
            obj.material.dispose();
          }
        });

        hasKey = false;
        stamina = MAX_STAMINA;
        flashlightBattery = getMaxFlashlightBattery();
        spareBatteries = 1; // Başlangıçta 1 pil

        // Labirent oluştur
        const mazeWidth = 13 + levelNum * 2;
        const mazeHeight = 13 + levelNum * 2;
        mazeMap = generateMaze(mazeWidth, mazeHeight);
        visitedMap = Array(mazeHeight)
          .fill(null)
          .map(() => Array(mazeWidth).fill(false));

        minimapCanvas.width = mazeWidth * MINIMAP_CELL_SIZE;
        minimapCanvas.height = mazeHeight * MINIMAP_CELL_SIZE;

        const emptySpaces = [];
        const deadEnds = [];

        for (let i = 0; i < mazeHeight; i++) {
          for (let j = 0; j < mazeWidth; j++) {
            if (mazeMap[i][j] === 0) {
              emptySpaces.push({ x: j, z: i });
              let neighborWalls = 0;
              if (i > 0 && mazeMap[i - 1][j] === 1) neighborWalls++;
              if (i < mazeHeight - 1 && mazeMap[i + 1][j] === 1)
                neighborWalls++;
              if (j > 0 && mazeMap[i][j - 1] === 1) neighborWalls++;
              if (j < mazeWidth - 1 && mazeMap[i][j + 1] === 1) neighborWalls++;
              if (neighborWalls >= 3) deadEnds.push({ x: j, z: i });
            }
          }
        }

        // Doku ve materyaller
        const wallTexture = textureLoader.load(
          "https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/brick_diffuse.jpg",
          undefined,
          undefined,
          () => wallMaterial.color.set(0x555555)
        );
        wallTexture.wrapS = THREE.RepeatWrapping;
        wallTexture.wrapT = THREE.RepeatWrapping;
        wallTexture.repeat.set(1, 1);
        const wallMaterial = new THREE.MeshStandardMaterial({
          map: wallTexture,
          roughness: 0.9,
        });

        const floorTexture = textureLoader.load(
          "https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/hardwood2_diffuse.jpg",
          undefined,
          undefined,
          () => floorMaterial.color.set(0x333333)
        );
        floorTexture.wrapS = THREE.RepeatWrapping;
        floorTexture.wrapT = THREE.RepeatWrapping;
        floorTexture.repeat.set(mazeWidth / 2, mazeHeight / 2);
        const floorMaterial = new THREE.MeshStandardMaterial({
          map: floorTexture,
          roughness: 0.8,
        });

        // Duvarları ve zemini yerleştir
        for (let i = 0; i < mazeHeight; i++) {
          for (let j = 0; j < mazeWidth; j++) {
            if (mazeMap[i][j] === 1) {
              const wallGeometry = new THREE.BoxGeometry(
                WALL_SIZE,
                WALL_HEIGHT,
                WALL_SIZE
              );
              const wall = new THREE.Mesh(wallGeometry, wallMaterial);
              wall.position.set(
                gridToWorld(j, mazeWidth),
                WALL_HEIGHT / 2,
                gridToWorld(i, mazeHeight)
              );
              wall.castShadow = true;
              wall.receiveShadow = true;
              scene.add(wall);
              collidableObjects.push(wall);
            }
          }
        }

        const floor = new THREE.Mesh(
          new THREE.PlaneGeometry(
            mazeWidth * WALL_SIZE,
            mazeHeight * WALL_SIZE
          ),
          floorMaterial
        );
        floor.rotation.x = -Math.PI / 2;
        floor.receiveShadow = true;
        scene.add(floor);

        // Optimal spawn positioning with maximum distance constraints

        // Step 1: Place player at a random position (starting point)
        const playerStartIndex = Math.floor(
          (Math.random() * emptySpaces.length) / 4
        );
        const playerStartPos = emptySpaces.splice(playerStartIndex, 1)[0];
        player.position.set(
          gridToWorld(playerStartPos.x, mazeWidth),
          PLAYER_BODY_SIZE.y / 2,
          gridToWorld(playerStartPos.z, mazeHeight)
        );

        // Step 2: Find door position that maximizes distance from player
        const doorResult = findMaxDistancePosition(emptySpaces, playerStartPos);
        let doorGridPos;
        if (doorResult.position) {
          doorGridPos = doorResult.position;
          emptySpaces.splice(doorResult.index, 1);
        } else {
          // Fallback to corner position if no optimal position found
          doorGridPos = { x: mazeWidth - 2, z: mazeHeight - 2 };
        }

        mazeMap[doorGridPos.z][doorGridPos.x] = 2; // Mark as door
        const doorPos = gridToWorldCoords(
          doorGridPos.x,
          doorGridPos.z,
          mazeWidth,
          mazeHeight
        );
        const doorTexture = textureLoader.load(
          "https://raw.githubusercontent.com/mrdoob/three.js/master/examples/textures/door.jpg",
          undefined,
          undefined,
          () => doorMaterial.color.set(0x8b4513)
        );
        const doorMaterial = new THREE.MeshStandardMaterial({
          map: doorTexture,
        });
        door = new THREE.Mesh(
          new THREE.BoxGeometry(WALL_SIZE, 3, 0.2),
          doorMaterial
        );
        door.position.set(doorPos.x, 1.5, doorPos.z);
        scene.add(door);
        collidableObjects.push(door);

        // Step 3: Find key position that maximizes distance from both player and door
        const keyResult = findOptimalSpawnPosition(emptySpaces, [
          playerStartPos,
          doorGridPos,
        ]);
        let keyPos;
        if (keyResult.position) {
          keyPos = keyResult.position;
          emptySpaces.splice(keyResult.index, 1);
        } else {
          // Fallback to random position if no optimal position found
          const keyIndex = Math.floor(Math.random() * emptySpaces.length);
          keyPos = emptySpaces.splice(keyIndex, 1)[0];
        }

        const keyWorldPos = gridToWorldCoords(
          keyPos.x,
          keyPos.z,
          mazeWidth,
          mazeHeight
        );
        const keyMaterial = new THREE.MeshStandardMaterial({
          color: 0xffd700,
          metalness: 1,
          roughness: 0.2,
          emissive: 0xffd700,
          emissiveIntensity: 0.5,
        });
        key = new THREE.Mesh(
          new THREE.TorusGeometry(0.15, 0.05, 8, 24),
          keyMaterial
        );
        key.position.set(keyWorldPos.x, 0.5, keyWorldPos.z);
        key.name = "key";
        scene.add(key);
        collectibles.push(key);

        for (let i = 0; i < 3; i++) {
          if (emptySpaces.length === 0) break;
          const battIndex = Math.floor(Math.random() * emptySpaces.length);
          const battPos = emptySpaces.splice(battIndex, 1)[0];
          const battWorldPos = gridToWorldCoords(
            battPos.x,
            battPos.z,
            mazeWidth,
            mazeHeight
          );
          const batteryMaterial = new THREE.MeshStandardMaterial({
            color: 0x33ff55,
            metalness: 0.5,
            roughness: 0.3,
            emissive: 0x33ff55,
            emissiveIntensity: 0.1,
          });
          const battery = new THREE.Mesh(
            new THREE.CylinderGeometry(0.1, 0.1, 0.25, 8),
            batteryMaterial
          );
          battery.position.set(battWorldPos.x, 0.125, battWorldPos.z);
          battery.rotation.z = Math.PI / 2;
          battery.name = "battery";
          scene.add(battery);
          collectibles.push(battery);
        }

        // Spawn coins (3-5 per level)
        const numCoins = Math.floor(Math.random() * 3) + 3; // 3-5 coins
        for (let i = 0; i < numCoins; i++) {
          if (emptySpaces.length === 0) break;
          const coinIndex = Math.floor(Math.random() * emptySpaces.length);
          const coinPos = emptySpaces.splice(coinIndex, 1)[0];
          const coinWorldPos = gridToWorldCoords(
            coinPos.x,
            coinPos.z,
            mazeWidth,
            mazeHeight
          );

          const coin = createCoinModel();
          coin.position.set(coinWorldPos.x, 0.5, coinWorldPos.z);
          scene.add(coin);
          coins.push(coin);
        }

        const closetMaterial = new THREE.MeshStandardMaterial({
          color: 0x403020,
        });
        for (const pos of deadEnds) {
          if (Math.random() < 0.3) {
            const closetWorldPos = gridToWorldCoords(
              pos.x,
              pos.z,
              mazeWidth,
              mazeHeight
            );
            const closet = new THREE.Mesh(
              new THREE.BoxGeometry(1.5, 2.5, 1),
              closetMaterial
            );
            closet.position.set(closetWorldPos.x, 1.25, closetWorldPos.z);
            closet.name = "closet";
            scene.add(closet);
            interactables.push(closet);
          }
        }

        // Always spawn exactly 1 enemy per level
        const numEnemies = 1;
        const enemySpeed = ENEMY_BASE_SPEED + (levelNum - 1) * 0.4;

        for (let i = 0; i < numEnemies; i++) {
          if (emptySpaces.length === 0) break;

          // Step 4: Position enemy near the door for strategic gameplay tension
          let enemyStartPos;

          // Find positions close to the door (within 3-5 maze cells)
          const nearDoorPositions = emptySpaces.filter((pos) => {
            const distance = calculateMazeDistance(pos, doorGridPos);
            return distance >= 3 && distance <= 5;
          });

          if (nearDoorPositions.length > 0) {
            // Choose a random position near the door
            const nearDoorIndex = Math.floor(
              Math.random() * nearDoorPositions.length
            );
            enemyStartPos = nearDoorPositions[nearDoorIndex];
            // Remove from emptySpaces
            const originalIndex = emptySpaces.findIndex(
              (pos) => pos.x === enemyStartPos.x && pos.z === enemyStartPos.z
            );
            if (originalIndex !== -1) {
              emptySpaces.splice(originalIndex, 1);
            }
          } else {
            // Fallback to random position if no near-door positions available
            const enemyIndex = Math.floor(Math.random() * emptySpaces.length);
            enemyStartPos = emptySpaces.splice(enemyIndex, 1)[0];
          }

          const enemyWorldPos = gridToWorldCoords(
            enemyStartPos.x,
            enemyStartPos.z,
            mazeWidth,
            mazeHeight
          );

          const enemyModel = createEnemyModel();
          enemyModel.position.set(enemyWorldPos.x, 1.0, enemyWorldPos.z);

          const enemyObject = {
            model: enemyModel,
            speed: enemySpeed,
            path: [],
            pathCooldown: 0,
            footstepCooldown: Math.random() * 0.5 + 0.5,
            aiState: ENEMY_AI_STATE.PATROL,
            lastKnownPlayerPos: null,
            searchTimer: 0,
            stunned: false,
            stunTimer: 0,
          };
          scene.add(enemyModel);
          enemies.push(enemyObject);
        }
      }

      function gridToWorld(coord, max) {
        return (coord - max / 2) * WALL_SIZE;
      }
      function gridToWorldCoords(x, z, w, h) {
        return { x: gridToWorld(x, w), z: gridToWorld(z, h) };
      }
      function worldToGrid(coord, max) {
        return Math.round(coord / WALL_SIZE + max / 2);
      }
      function worldToGridCoords(x, z, w, h) {
        return { x: worldToGrid(x, w), z: worldToGrid(z, h) };
      }

      /**
       * Calculate the actual maze distance between two points using pathfinding
       */
      function calculateMazeDistance(startPos, endPos) {
        const path = findPath(startPos, endPos);
        return path ? path.length : Infinity;
      }

      /**
       * Find the position in emptySpaces that maximizes distance from a reference point
       */
      function findMaxDistancePosition(emptySpaces, referencePos) {
        let maxDistance = 0;
        let bestPosition = null;
        let bestIndex = -1;

        for (let i = 0; i < emptySpaces.length; i++) {
          const distance = calculateMazeDistance(referencePos, emptySpaces[i]);
          if (distance > maxDistance) {
            maxDistance = distance;
            bestPosition = emptySpaces[i];
            bestIndex = i;
          }
        }

        return {
          position: bestPosition,
          index: bestIndex,
          distance: maxDistance,
        };
      }

      /**
       * Find the position that maximizes the minimum distance to multiple reference points
       */
      function findOptimalSpawnPosition(emptySpaces, referencePositions) {
        let maxMinDistance = 0;
        let bestPosition = null;
        let bestIndex = -1;

        for (let i = 0; i < emptySpaces.length; i++) {
          let minDistance = Infinity;

          // Calculate minimum distance to all reference points
          for (const refPos of referencePositions) {
            const distance = calculateMazeDistance(emptySpaces[i], refPos);
            minDistance = Math.min(minDistance, distance);
          }

          // Keep the position with the maximum minimum distance
          if (minDistance > maxMinDistance) {
            maxMinDistance = minDistance;
            bestPosition = emptySpaces[i];
            bestIndex = i;
          }
        }

        return {
          position: bestPosition,
          index: bestIndex,
          distance: maxMinDistance,
        };
      }

      /**
       * Check if a position would cause collision with walls for enemy
       */
      function checkEnemyCollision(position) {
        const enemySize = new THREE.Vector3(0.8, 2.0, 0.8); // Enemy bounding box size
        const enemyCollider = new THREE.Box3().setFromCenterAndSize(
          position,
          enemySize
        );

        for (const wall of collidableObjects) {
          const wallCollider = new THREE.Box3().setFromObject(wall);
          if (enemyCollider.intersectsBox(wallCollider)) {
            return true; // Collision detected
          }
        }
        return false; // No collision
      }

      /**
       * Get a safe position for enemy movement that doesn't clip through walls
       */
      function getSafeEnemyPosition(currentPosition, targetPosition) {
        const direction = new THREE.Vector3().subVectors(
          targetPosition,
          currentPosition
        );
        const distance = direction.length();

        if (distance === 0) return currentPosition.clone();

        direction.normalize();

        // Test positions along the movement vector
        const steps = Math.ceil(distance / 0.1); // Test every 0.1 units
        for (let i = 1; i <= steps; i++) {
          const testPosition = currentPosition
            .clone()
            .add(direction.clone().multiplyScalar((distance * i) / steps));

          if (checkEnemyCollision(testPosition)) {
            // Return the last safe position
            if (i === 1) return currentPosition.clone();
            return currentPosition
              .clone()
              .add(
                direction.clone().multiplyScalar((distance * (i - 1)) / steps)
              );
          }
        }

        return targetPosition.clone(); // Target position is safe
      }

      /**
       * A* yol bulma algoritması.
       */
      function findPath(startPos, endPos) {
        const grid = mazeMap;
        if (!grid[endPos.z] || grid[endPos.z][endPos.x] === 1) return []; // Hedef geçersiz

        const openList = [];
        const closedList = new Set();
        const startNode = {
          x: startPos.x,
          z: startPos.z,
          g: 0,
          h: 0,
          f: 0,
          parent: null,
        };
        startNode.h =
          Math.abs(startNode.x - endPos.x) + Math.abs(startNode.z - endPos.z);
        startNode.f = startNode.h;
        openList.push(startNode);

        let maxIterations = grid.length * grid[0].length;
        while (openList.length > 0 && maxIterations > 0) {
          maxIterations--;
          let lowestFIndex = 0;
          for (let i = 1; i < openList.length; i++) {
            if (openList[i].f < openList[lowestFIndex].f) lowestFIndex = i;
          }
          const currentNode = openList.splice(lowestFIndex, 1)[0];
          closedList.add(`${currentNode.x},${currentNode.z}`);

          if (currentNode.x === endPos.x && currentNode.z === endPos.z) {
            let path = [];
            let current = currentNode;
            while (current) {
              path.push({ x: current.x, z: current.z });
              current = current.parent;
            }
            return path.reverse();
          }

          const neighbors = [];
          const { x, z } = currentNode;
          if (grid[z - 1] && grid[z - 1][x] !== 1)
            neighbors.push({ x, z: z - 1 });
          if (grid[z + 1] && grid[z + 1][x] !== 1)
            neighbors.push({ x, z: z + 1 });
          if (grid[z][x - 1] !== 1) neighbors.push({ x: x - 1, z });
          if (grid[z][x + 1] !== 1) neighbors.push({ x: x + 1, z });

          for (const neighborPos of neighbors) {
            if (closedList.has(`${neighborPos.x},${neighborPos.z}`)) continue;

            const gScore = currentNode.g + 1;
            let existingNode = openList.find(
              (n) => n.x === neighborPos.x && n.z === neighborPos.z
            );

            if (!existingNode || gScore < existingNode.g) {
              if (!existingNode) {
                existingNode = { x: neighborPos.x, z: neighborPos.z };
                existingNode.h =
                  Math.abs(existingNode.x - endPos.x) +
                  Math.abs(existingNode.z - endPos.z);
                openList.push(existingNode);
              }
              existingNode.parent = currentNode;
              existingNode.g = gScore;
              existingNode.f = existingNode.g + existingNode.h;
            }
          }
        }
        return []; // Yol bulunamadı
      }

      /**
       * Fare, klavye ve pencere olay dinleyicilerini ayarlar.
       */
      function setupEventListeners() {
        window.addEventListener("resize", onWindowResize, false);
        camera.rotation.order = "YXZ";
        const startScreen = document.getElementById("start-screen");
        const gameOverScreen = document.getElementById("game-over-screen");

        if (!startScreen || !gameOverScreen) {
          console.error("Required DOM elements not found");
          return;
        }
        const uiElements = [
          document.getElementById("crosshair"),
          document.getElementById("level-display"),
          document.getElementById("money-display"),
          document.getElementById("stamina-container"),
          document.getElementById("battery-container"),
          document.getElementById("spare-batteries-ui"),
          document.getElementById("weapon-display"),
          document.getElementById("minimap-container"),
        ];

        startScreen.addEventListener("click", () => {
          try {
            initAudio();
            level = 1;
            loadLevel(level);
            document.body.requestPointerLock();
          } catch (error) {
            console.error("Start screen error:", error);
          }
        });
        gameOverScreen.addEventListener("click", () => {
          try {
            isGameOver = false;
            level = 1;
            loadLevel(level);
            document.body.requestPointerLock();
          } catch (error) {
            console.error("Game over screen error:", error);
          }
        });

        // Mouse click for weapon firing - only listen when pointer is locked
        document.addEventListener("click", (event) => {
          if (
            gameActive &&
            !isShopOpen &&
            document.pointerLockElement === document.body
          ) {
            fireWeapon();
          }
        });

        document.addEventListener(
          "pointerlockchange",
          () => {
            gameActive = document.pointerLockElement === document.body;
            const uiElements = [
              document.getElementById("crosshair"),
              document.getElementById("level-display"),
              document.getElementById("money-display"),
              document.getElementById("stamina-container"),
              document.getElementById("battery-container"),
              document.getElementById("spare-batteries-ui"),
              document.getElementById("weapon-display"),
              document.getElementById("minimap-container"),
            ];
            uiElements.forEach(
              (el) => (el.style.display = gameActive ? "block" : "none")
            );
            document.getElementById("hiding-overlay").style.display = isHiding
              ? "flex"
              : "none";

            if (!gameActive) {
              if (isGameOver) {
                document.getElementById("game-over-screen").style.display =
                  "flex";
                document.getElementById(
                  "game-over-screen"
                ).style.flexDirection = "column";
              } else {
                document.getElementById("start-screen").style.display = "flex";
                document.getElementById("start-screen").style.flexDirection =
                  "column";
              }
            } else {
              document.getElementById("start-screen").style.display = "none";
              document.getElementById("game-over-screen").style.display =
                "none";
            }
          },
          false
        );

        document.addEventListener("mousemove", (event) => {
          if (!gameActive || isHiding) return;
          player.rotation.y -= event.movementX * 0.002;
          camera.rotation.x -= event.movementY * 0.002;
          camera.rotation.x = Math.max(
            -Math.PI / 2,
            Math.min(Math.PI / 2, camera.rotation.x)
          );
        });

        document.addEventListener("keydown", (event) => {
          console.log(
            "Key pressed:",
            event.code,
            "gameActive:",
            gameActive,
            "isHiding:",
            isHiding
          );

          // Handle TAB and ESC regardless of game state
          if (event.code === "Tab") {
            event.preventDefault();
            console.log("TAB pressed, isShopOpen:", isShopOpen);
            if (isShopOpen) {
              console.log("Closing shop...");
              closeShop();
            } else {
              console.log("Opening shop...");
              openShop();
            }
            return;
          }

          if (event.code === "Escape") {
            if (isShopOpen) {
              console.log("ESC pressed, closing shop...");
              closeShop();
            }
            return;
          }

          if (event.code === "KeyE" && isHiding) {
            interact();
            return;
          }
          if (!gameActive || isHiding) return;
          switch (event.code) {
            case "KeyW":
            case "ArrowUp":
              moveState.forward = true;
              break;
            case "KeyS":
            case "ArrowDown":
              moveState.backward = true;
              break;
            case "KeyA":
            case "ArrowLeft":
              moveState.left = true;
              break;
            case "KeyD":
            case "ArrowRight":
              moveState.right = true;
              break;
            case "ShiftLeft":
            case "ShiftRight":
              moveState.sprint = true;
              break;
            case "KeyF":
              isFlashlightOn = !isFlashlightOn;
              flashlight.visible = isFlashlightOn;
              break;
            case "KeyR":
              if (
                spareBatteries > 0 &&
                flashlightBattery < getMaxFlashlightBattery()
              ) {
                spareBatteries--;
                flashlightBattery = getMaxFlashlightBattery();
                if (audioInitialized) sounds.reload.triggerAttackRelease("4n");
              } else {
                if (audioInitialized)
                  sounds.reloadFail.triggerAttackRelease("C2", "16n");
              }
              break;
            case "KeyE":
              interact();
              break;
          }
        });

        document.addEventListener("keyup", (event) => {
          switch (event.code) {
            case "KeyW":
            case "ArrowUp":
              moveState.forward = false;
              break;
            case "KeyS":
            case "ArrowDown":
              moveState.backward = false;
              break;
            case "KeyA":
            case "ArrowLeft":
              moveState.left = false;
              break;
            case "KeyD":
            case "ArrowRight":
              moveState.right = false;
              break;
            case "ShiftLeft":
            case "ShiftRight":
              moveState.sprint = false;
              break;
          }
        });
      }

      function onWindowResize() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      }

      /**
       * Oyuncu etkileşimlerini yönetir (toplanabilirler, kapılar, saklanma).
       */
      function interact() {
        if (isHiding) {
          isHiding = false;
          if (audioInitialized) sounds.unhide.triggerAttackRelease("D2", "8n");
          document.getElementById("hiding-overlay").style.display = "none";
          return;
        }

        for (const item of interactables) {
          if (player.position.distanceTo(item.position) < 2.0) {
            if (item.name === "closet") {
              isHiding = true;
              if (audioInitialized)
                sounds.hide.triggerAttackRelease("C2", "8n");
              document.getElementById("hiding-overlay").style.display = "flex";
              return;
            }
          }
        }

        for (let i = collectibles.length - 1; i >= 0; i--) {
          const item = collectibles[i];
          if (player.position.distanceTo(item.position) < 1.5) {
            if (item.name === "key") {
              if (audioInitialized)
                sounds.keyPickup.triggerAttackRelease("C5", "8n");
              hasKey = true;
              showMessage("key-message");
            } else if (item.name === "battery") {
              if (audioInitialized)
                sounds.batteryPickup.triggerAttackRelease("G5", "16n");
              spareBatteries++;
            }
            scene.remove(item);
            collectibles.splice(i, 1);
            return;
          }
        }

        // Check for coin collection
        for (let i = coins.length - 1; i >= 0; i--) {
          const coin = coins[i];
          if (player.position.distanceTo(coin.position) < 1.5) {
            if (audioInitialized)
              sounds.coinPickup.triggerAttackRelease("E5", "8n");
            playerMoney += 10; // Each coin gives 10 money
            saveGame(); // Save progress when collecting coins
            scene.remove(coin);
            coins.splice(i, 1);
            return;
          }
        }

        if (door && player.position.distanceTo(door.position) < 2.5) {
          if (hasKey) {
            // Award level completion bonus
            playerMoney += LEVEL_COMPLETION_BONUS;
            level++;
            saveGame(); // Save progress
            loadLevel(level);
          } else {
            if (audioInitialized)
              sounds.doorLocked.triggerAttackRelease("C2", "8n");
            showMessage("door-locked-message");
          }
        }
      }

      function showMessage(elementId, duration = 3000) {
        const msg = document.getElementById(elementId);
        msg.style.display = "block";
        setTimeout(() => {
          msg.style.display = "none";
        }, duration);
      }

      /**
       * Oyuncu hareketini, dayanıklılığını ve kamera sallanmasını günceller.
       */
      function updatePlayer(deltaTime) {
        if (isHiding) return;
        const isMoving =
          moveState.forward ||
          moveState.backward ||
          moveState.left ||
          moveState.right;
        let currentSpeed = PLAYER_BASE_SPEED;

        // Apply speed upgrade
        if (upgrades.speed) {
          currentSpeed *= 1.5; // 50% speed increase
        }

        if (moveState.sprint && isMoving && stamina > 0) {
          currentSpeed *= SPRINT_MULTIPLIER;
          stamina -= STAMINA_DRAIN_RATE * deltaTime;
          staminaRegenCooldown = STAMINA_REGEN_DELAY;
        } else {
          if (staminaRegenCooldown > 0) {
            staminaRegenCooldown -= deltaTime;
          } else if (stamina < MAX_STAMINA) {
            stamina += STAMINA_REGEN_RATE * deltaTime;
          }
        }
        stamina = Math.max(0, Math.min(MAX_STAMINA, stamina));

        const moveDirection = new THREE.Vector3(
          (moveState.right ? 1 : 0) - (moveState.left ? 1 : 0),
          0,
          (moveState.backward ? 1 : 0) - (moveState.forward ? 1 : 0)
        );
        moveDirection.normalize().applyQuaternion(player.quaternion);

        playerVelocity.x = moveDirection.x * currentSpeed;
        playerVelocity.z = moveDirection.z * currentSpeed;

        const playerDeltaX = playerVelocity.clone().multiplyScalar(deltaTime);
        playerDeltaX.z = 0;
        player.position.add(playerDeltaX);
        checkCollisions();

        const playerDeltaZ = playerVelocity.clone().multiplyScalar(deltaTime);
        playerDeltaZ.x = 0;
        player.position.add(playerDeltaZ);
        checkCollisions();

        if (isMoving) {
          bobTime += deltaTime * (moveState.sprint ? 12 : 8);
          camera.position.y = PLAYER_HEIGHT * 0.4 + Math.sin(bobTime) * 0.05;
          footstepCooldown -= deltaTime;
          if (audioInitialized && footstepCooldown <= 0) {
            sounds.footstep.triggerAttackRelease("C1", "8n");
            footstepCooldown = moveState.sprint ? 0.3 : 0.5;
          }
        } else {
          camera.position.y = PLAYER_HEIGHT * 0.4;
        }
      }

      /**
       * Oyuncunun duvarlarla çarpışmasını daha yumuşak bir şekilde yönetir.
       */
      function checkCollisions() {
        playerCollider.setFromCenterAndSize(
          player.position.clone().add(playerColliderMesh.position),
          PLAYER_BODY_SIZE
        );

        for (const box of collidableObjects) {
          const boxCollider = new THREE.Box3().setFromObject(box);
          if (playerCollider.intersectsBox(boxCollider)) {
            const intersection = new THREE.Box3();
            intersection.copy(playerCollider).intersect(boxCollider);

            const penetration = new THREE.Vector3();
            penetration.x = intersection.max.x - intersection.min.x;
            penetration.y = intersection.max.y - intersection.min.y;
            penetration.z = intersection.max.z - intersection.min.z;

            const playerCenter = new THREE.Vector3();
            playerCollider.getCenter(playerCenter);
            const boxCenter = new THREE.Vector3();
            boxCollider.getCenter(boxCenter);
            const toPlayer = playerCenter.sub(boxCenter);

            if (penetration.x < penetration.z) {
              player.position.x +=
                toPlayer.x > 0 ? penetration.x : -penetration.x;
            } else {
              player.position.z +=
                toPlayer.z > 0 ? penetration.z : -penetration.z;
            }
          }
        }
      }

      async function callGemini(prompt) {
        const apiKey = "";
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
        const payload = { contents: [{ parts: [{ text: prompt }] }] };
        try {
          const response = await fetch(apiUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });
          const result = await response.json();
          if (
            result.candidates &&
            result.candidates[0].content &&
            result.candidates[0].content.parts[0]
          ) {
            return result.candidates[0].content.parts[0].text.replace(
              /["*]/g,
              ""
            );
          }
        } catch (error) {
          console.error("Gemini API call failed:", error);
          return null;
        }
        return null;
      }

      async function updateWhispers(deltaTime) {
        whisperCooldown -= deltaTime;
        if (whisperCooldown > 0) return;
        whisperCooldown = 20.0 + Math.random() * 10;

        let prompt = null;
        const nearestEnemyDist =
          enemies.length > 0
            ? player.position.distanceTo(enemies[0].model.position)
            : Infinity;

        if (flashlightBattery < 20) {
          prompt =
            "You are a monster in a dark maze. The player's flashlight is dying. Whisper a short, scary, one-sentence phrase in Turkish to taunt them about the coming darkness.";
        } else if (stamina < 20) {
          prompt =
            "You are a monster in a dark maze. The player is exhausted and out of breath. Whisper a short, scary, one-sentence phrase in Turkish to taunt them about their fatigue.";
        } else if (nearestEnemyDist < 7 && !isFlashlightOn) {
          prompt =
            "You are a monster in a dark maze, right behind the player who cannot see you. Whisper a short, scary, one-sentence phrase in Turkish.";
        }

        if (prompt) {
          const whisperText = await callGemini(prompt);
          if (whisperText) {
            const whisperElement = document.getElementById("whisper-message");
            whisperElement.innerText = whisperText;
            whisperElement.style.opacity = 1;
            setTimeout(() => {
              whisperElement.style.opacity = 0;
            }, 4000);
          }
        }
      }

      /**
       * Düşmanların yapay zekasını ve hareketini yönetir.
       * (PATROL, CHASE, SEARCH durumları ile daha gelişmiş mantık)
       */
      function updateEnemies(deltaTime) {
        const flashlightPosition = flashlight.getWorldPosition(
          new THREE.Vector3()
        );
        const flashlightDirection = new THREE.Vector3();
        camera.getWorldDirection(flashlightDirection);

        enemies.forEach((enemy) => {
          const { model } = enemy;
          if (!model || !model.material) return;

          // Handle stunning
          if (enemy.stunned) {
            enemy.stunTimer -= deltaTime;
            if (enemy.stunTimer <= 0) {
              enemy.stunned = false;
              enemy.stunTimer = 0;
            } else {
              // Enemy is stunned, skip AI updates but show visual effect
              if (model.material.emissive) {
                model.material.emissive.setHex(0x0066ff); // Blue glow when stunned
              }
              return;
            }
          } else {
            // Reset material when not stunned
            if (model.material.emissive) {
              model.material.emissive.setHex(0x330000);
            }
          }

          const distanceToPlayer = player.position.distanceTo(model.position);

          // Oyuncu Görüş Alanında mı? (LOS - Line of Sight)
          const directionToPlayer = new THREE.Vector3()
            .subVectors(player.position, model.position)
            .normalize();
          raycaster.set(model.position, directionToPlayer);
          const wallIntersects = raycaster.intersectObjects(collidableObjects);
          const hasLineOfSight =
            wallIntersects.length === 0 ||
            wallIntersects[0].distance > distanceToPlayer;

          // Oyuncu saklanıyor mu?
          if (isHiding) {
            if (enemy.aiState === ENEMY_AI_STATE.CHASE) {
              enemy.aiState = ENEMY_AI_STATE.SEARCH;
              enemy.searchTimer = 5.0; // 5 saniye boyunca son görülen yerde ara
            }
          } else if (hasLineOfSight && distanceToPlayer < 15) {
            enemy.aiState = ENEMY_AI_STATE.CHASE;
            enemy.lastKnownPlayerPos = player.position.clone();
          } else {
            if (enemy.aiState === ENEMY_AI_STATE.CHASE) {
              enemy.aiState = ENEMY_AI_STATE.SEARCH; // Görüş kaybedildi, aramaya başla
              enemy.searchTimer = 5.0;
            }
          }

          // Düşman Işıkta mı?
          const vectorToEnemy = new THREE.Vector3()
            .subVectors(model.position, flashlightPosition)
            .normalize();
          const dotProduct = flashlightDirection.dot(vectorToEnemy);
          const angleToEnemy = Math.acos(dotProduct);
          const inFlashlightCone = angleToEnemy < flashlight.angle;
          let isLit = false;
          if (
            isFlashlightOn &&
            inFlashlightCone &&
            distanceToPlayer < flashlight.distance
          ) {
            raycaster.set(flashlightPosition, vectorToEnemy);
            const intersects = raycaster.intersectObjects(collidableObjects);
            if (
              intersects.length === 0 ||
              intersects[0].distance > distanceToPlayer
            ) {
              isLit = true;
            }
          }

          // Davranış
          if (isLit) {
            // Işıkta donup kal
            enemy.path = []; // Hareket etmemesi için yolu temizle

            // Oyuncu çok yaklaşırsa hafifçe geri çekil (with collision detection)
            if (distanceToPlayer < 2.5) {
              const pushDirection = new THREE.Vector3()
                .subVectors(model.position, player.position)
                .normalize();
              pushDirection.y = 0;

              // Calculate target retreat position
              const retreatDistance = enemy.speed * 0.5 * deltaTime;
              const targetPosition = model.position
                .clone()
                .add(pushDirection.multiplyScalar(retreatDistance));

              // Use collision detection to ensure safe movement
              const safePosition = getSafeEnemyPosition(
                model.position,
                targetPosition
              );
              model.position.copy(safePosition);
            }
            // Eğer oyuncu yakın değilse, düşman sadece duracak.
          } else {
            // Normal AI
            enemy.pathCooldown -= deltaTime;
            switch (enemy.aiState) {
              case ENEMY_AI_STATE.CHASE:
                if (enemy.pathCooldown <= 0) {
                  enemy.pathCooldown = 1.0;
                  const enemyGrid = worldToGridCoords(
                    model.position.x,
                    model.position.z,
                    mazeMap[0].length,
                    mazeMap.length
                  );
                  const playerGrid = worldToGridCoords(
                    player.position.x,
                    player.position.z,
                    mazeMap[0].length,
                    mazeMap.length
                  );
                  enemy.path = findPath(enemyGrid, playerGrid);
                }
                break;
              case ENEMY_AI_STATE.SEARCH:
                enemy.searchTimer -= deltaTime;
                if (enemy.searchTimer <= 0) {
                  enemy.aiState = ENEMY_AI_STATE.PATROL;
                }
                if (enemy.pathCooldown <= 0 && enemy.lastKnownPlayerPos) {
                  enemy.pathCooldown = 1.5;
                  const enemyGrid = worldToGridCoords(
                    model.position.x,
                    model.position.z,
                    mazeMap[0].length,
                    mazeMap.length
                  );
                  const targetGrid = worldToGridCoords(
                    enemy.lastKnownPlayerPos.x,
                    enemy.lastKnownPlayerPos.z,
                    mazeMap[0].length,
                    mazeMap.length
                  );
                  enemy.path = findPath(enemyGrid, targetGrid);
                  if (!enemy.path || enemy.path.length <= 1) {
                    enemy.aiState = ENEMY_AI_STATE.PATROL;
                  }
                }
                break;
              case ENEMY_AI_STATE.PATROL:
                if (!enemy.path || enemy.path.length <= 1) {
                  const emptySpaces = [];
                  for (let i = 0; i < mazeMap.length; i++) {
                    for (let j = 0; j < mazeMap[0].length; j++) {
                      if (mazeMap[i][j] === 0) emptySpaces.push({ x: j, z: i });
                    }
                  }
                  const randomTarget =
                    emptySpaces[Math.floor(Math.random() * emptySpaces.length)];
                  const enemyGrid = worldToGridCoords(
                    model.position.x,
                    model.position.z,
                    mazeMap[0].length,
                    mazeMap.length
                  );
                  enemy.path = findPath(enemyGrid, randomTarget);
                }
                break;
            }

            // Yolu takip et (with collision detection)
            if (enemy.path && enemy.path.length > 1) {
              const targetGridPos = enemy.path[1];
              const targetWorldPos = gridToWorldCoords(
                targetGridPos.x,
                targetGridPos.z,
                mazeMap[0].length,
                mazeMap.length
              );
              targetWorldPos.y = model.position.y;

              // Calculate intended movement
              const directionToTarget = new THREE.Vector3()
                .subVectors(targetWorldPos, model.position)
                .normalize();
              const intendedPosition = model.position
                .clone()
                .add(directionToTarget.multiplyScalar(enemy.speed * deltaTime));

              // Use collision detection to ensure safe movement
              const safePosition = getSafeEnemyPosition(
                model.position,
                intendedPosition
              );
              model.position.copy(safePosition);

              model.lookAt(
                targetWorldPos.x,
                model.position.y,
                targetWorldPos.z
              );

              if (model.position.distanceTo(targetWorldPos) < 0.5) {
                enemy.path.shift();
              }

              enemy.footstepCooldown -= deltaTime;
              if (audioInitialized && enemy.footstepCooldown <= 0) {
                const volume = Math.max(-30, 0 - distanceToPlayer * 1.5);
                sounds.enemyFootstep.volume.value = volume;
                sounds.enemyFootstep.triggerAttackRelease("C1", "8n");
                enemy.footstepCooldown = 0.7;
              }
            }
          }

          if (!isHiding && distanceToPlayer < 0.8) {
            gameOver();
          }
        });
      }

      function updateFlashlight(deltaTime) {
        if (isFlashlightOn && flashlightBattery > 0) {
          flashlightBattery -= FLASHLIGHT_DRAIN_RATE * deltaTime;
        }
        if (flashlightBattery <= 0) {
          flashlightBattery = 0;
          isFlashlightOn = false;
          flashlight.visible = false;
        }
        flashlight.intensity =
          BASE_FLASHLIGHT_INTENSITY *
          (0.2 + 0.8 * (flashlightBattery / getMaxFlashlightBattery()));
        // Fener titreme efekti
        if (isFlashlightOn && flashlightBattery < 25) {
          flashlight.intensity *= 1.0 - Math.random() * 0.2;
        }
      }

      function gameOver() {
        if (!isGameOver) {
          isGameOver = true;
          if (audioInitialized) sounds.gameOver.triggerAttackRelease("4n");
          gameActive = false;
          document.getElementById("game-over-taunt").innerText =
            "Gözcüler seni buldu.";
          document.exitPointerLock();
        }
      }

      function drawMinimap() {
        if (!gameActive || !mazeMap) return;
        minimapCtx.clearRect(0, 0, minimapCanvas.width, minimapCanvas.height);

        const playerGridPos = worldToGridCoords(
          player.position.x,
          player.position.z,
          mazeMap[0].length,
          mazeMap.length
        );

        // Haritayı aç
        const revealRadius = 1;
        for (let z = -revealRadius; z <= revealRadius; z++) {
          for (let x = -revealRadius; x <= revealRadius; x++) {
            const checkX = playerGridPos.x + x;
            const checkZ = playerGridPos.z + z;
            if (mazeMap[checkZ] && mazeMap[checkZ][checkX] !== undefined) {
              visitedMap[checkZ][checkX] = true;
            }
          }
        }

        // Haritayı çiz
        for (let z = 0; z < mazeMap.length; z++) {
          for (let x = 0; x < mazeMap[0].length; x++) {
            if (!visitedMap[z][x]) continue;
            if (mazeMap[z][x] === 1) {
              minimapCtx.fillStyle = "#666";
            } else if (mazeMap[z][x] === 2) {
              minimapCtx.fillStyle = "#5c3e1a";
            } else {
              minimapCtx.fillStyle = "#222";
            }
            minimapCtx.fillRect(
              x * MINIMAP_CELL_SIZE,
              z * MINIMAP_CELL_SIZE,
              MINIMAP_CELL_SIZE,
              MINIMAP_CELL_SIZE
            );
          }
        }
        // Nesneleri çiz
        if (key && !hasKey) {
          const itemGridPos = worldToGridCoords(
            key.position.x,
            key.position.z,
            mazeMap[0].length,
            mazeMap.length
          );
          if (visitedMap[itemGridPos.z][itemGridPos.x]) {
            minimapCtx.fillStyle = "#ffd700";
            minimapCtx.fillRect(
              itemGridPos.x * MINIMAP_CELL_SIZE + 2,
              itemGridPos.z * MINIMAP_CELL_SIZE + 2,
              MINIMAP_CELL_SIZE - 4,
              MINIMAP_CELL_SIZE - 4
            );
          }
        }
        // Oyuncuyu çiz
        minimapCtx.fillStyle = "#fff";
        minimapCtx.beginPath();
        minimapCtx.arc(
          playerGridPos.x * MINIMAP_CELL_SIZE + MINIMAP_CELL_SIZE / 2,
          playerGridPos.z * MINIMAP_CELL_SIZE + MINIMAP_CELL_SIZE / 2,
          MINIMAP_CELL_SIZE / 2 - 1,
          0,
          Math.PI * 2
        );
        minimapCtx.fill();
      }

      /**
       * Ana oyun döngüsü. Her karede çağrılır.
       */
      function animate() {
        requestAnimationFrame(animate);
        const deltaTime = Math.min(0.05, clock.getDelta());

        // UI Güncelleme - Safe DOM access (only when game is active)
        if (gameActive) {
          try {
            const levelDisplay = document.getElementById("level-display");
            if (levelDisplay) levelDisplay.innerText = `Level: ${level}`;

            const moneyDisplay = document.getElementById("money-display");
            if (moneyDisplay) moneyDisplay.innerText = `Coins: ${playerMoney}`;

            const staminaBar = document.getElementById("stamina-bar");
            if (staminaBar)
              staminaBar.style.width = (stamina / MAX_STAMINA) * 100 + "%";

            const batteryBar = document.getElementById("battery-bar");
            if (batteryBar)
              batteryBar.style.width =
                (flashlightBattery / getMaxFlashlightBattery()) * 100 + "%";

            const spareBatteriesUI =
              document.getElementById("spare-batteries-ui");
            if (spareBatteriesUI)
              spareBatteriesUI.innerText = `🔋 ${spareBatteries}`;

            const weaponDisplay = document.getElementById("weapon-display");
            if (weaponDisplay)
              weaponDisplay.innerText = `⚡ ${weaponAmmo}/${MAX_WEAPON_AMMO}`;
          } catch (error) {
            console.error("UI update error:", error);
          }

          // Show/hide weapon display based on ownership
          const weaponDisplayForVisibility =
            document.getElementById("weapon-display");
          if (weaponDisplayForVisibility) {
            weaponDisplayForVisibility.style.display = upgrades.weapon
              ? "block"
              : "none";
          }
        }

        // Animate coins
        coins.forEach((coin) => {
          coin.rotation.y += deltaTime * coin.userData.rotationSpeed;
          coin.position.y =
            0.5 + Math.sin(Date.now() * 0.002 + coin.position.x) * 0.1;
        });

        if (gameActive) {
          updatePlayer(deltaTime);
          updateEnemies(deltaTime);
          updateFlashlight(deltaTime);
          updateWhispers(deltaTime);
          updateProjectiles(deltaTime);
          drawMinimap();
        }
        renderer.render(scene, camera);
      }
    </script>
  </body>
</html>
